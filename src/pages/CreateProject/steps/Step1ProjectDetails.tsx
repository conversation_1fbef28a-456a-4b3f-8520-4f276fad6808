import React from 'react';
import { TextInput, TextArea, PrimaryButton } from '../../../components/ui';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'firma';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

interface Step1ProjectDetailsProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
}

export const Step1ProjectDetails: React.FC<Step1ProjectDetailsProps> = ({
  formData,
  updateFormData,
  errors,
  onNext
}) => {
  // Validate step 1 fields
  const validateStep = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Project name validation
    if (!formData.projectName || formData.projectName.trim().length < 2) {
      newErrors.projectName = 'Prosjektnavn må være minst 2 tegn langt';
    }

    if (formData.projectName && formData.projectName.length > 100) {
      newErrors.projectName = 'Prosjektnavn kan ikke være lengre enn 100 tegn';
    }

    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      onNext();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleNext();
    }
  };

  return (
    <div className="space-y-6" onKeyDown={handleKeyDown}>
      {/* Project Name Field */}
      <TextInput
        label="Prosjektnavn"
        placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
        required
        fullWidth
        size="lg"
        value={formData.projectName}
        onChange={(e) => updateFormData({ projectName: e.target.value })}
        error={errors.projectName}
        autoFocus
      />

      {/* Description Field */}
      <TextArea
        label="Beskrivelse"
        placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
        fullWidth
        rows={4}
        value={formData.description}
        onChange={(e) => updateFormData({ description: e.target.value })}
        helperText="💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"
      />

      {/* Navigation */}
      <div className="flex justify-end pt-4">
        <PrimaryButton
          onClick={handleNext}
          disabled={!formData.projectName.trim()}
          size="lg"
          className="btn-wizard-lg"
        >
          Neste
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>

      {/* Keyboard Shortcut Hint */}
      <div className="text-center">
        <p className="text-xs text-jobblogg-text-muted">
          💡 Tips: Trykk Ctrl + Enter for å gå til neste steg
        </p>
      </div>
    </div>
  );
};

export default Step1ProjectDetails;
