import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, StepProgress, WizardStep } from '../../components/ui';
import { Step1ProjectDetails } from './steps/Step1ProjectDetails';
import { Step2CustomerInfo } from './steps/Step2CustomerInfo';
import { Step3JobDescription } from './steps/Step3JobDescription';

const STORAGE_KEY = 'jobblogg-create-project-wizard';

// Form data interface for the multi-step wizard
export interface WizardFormData {
  // Project details
  projectName: string;
  description: string;

  // Customer data
  customerName: string;
  customerType: 'privat' | 'firma';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  orgNumber: string;
  notes: string;

  // Job description
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

// Step validation interface
export interface StepValidation {
  isValid: boolean;
  errors: { [key: string]: string };
}



const CreateProjectWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [useExistingCustomer, setUseExistingCustomer] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
  const [createdProjectId, setCreatedProjectId] = useState<string | null>(null);

  const [formData, setFormData] = useState<WizardFormData>({
    projectName: '',
    description: '',
    customerName: '',
    customerType: 'privat',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    orgNumber: '',
    notes: '',
    jobDescription: '',
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: '',
    personalNotes: ''
  });

  const navigate = useNavigate();
  const createProject = useMutation(api.projects.create);
  const createCustomer = useMutation(api.customers.create);
  const updateProjectJobData = useMutation(api.projects.updateProjectJobData);
  const { user } = useUser();

  // Fetch existing customers for selection
  const existingCustomers = useQuery(
    api.customers.getByUser,
    user?.id ? { userId: user.id } : "skip"
  );

  // Define wizard steps
  const steps = [
    {
      id: 1,
      title: 'Prosjektdetaljer',
      description: 'Grunnleggende prosjektinformasjon',
      isCompleted: currentStep > 1,
      isActive: currentStep === 1
    },
    {
      id: 2,
      title: 'Kundeinformasjon',
      description: 'Kundedetaljer og kontaktinformasjon',
      isCompleted: currentStep > 2,
      isActive: currentStep === 2
    },
    {
      id: 3,
      title: 'Jobbeskrivelse',
      description: 'Detaljert jobbinformasjon og krav',
      isCompleted: currentStep > 3,
      isActive: currentStep === 3
    }
  ];

  // Autosave functionality with debouncing
  const saveToLocalStorage = useCallback(() => {
    const dataToSave = {
      formData,
      currentStep,
      useExistingCustomer,
      selectedCustomerId,
      createdProjectId,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [formData, currentStep, useExistingCustomer, selectedCustomerId, createdProjectId]);

  // Check if form has meaningful data for draft creation
  const hasFormData = useCallback(() => {
    return formData.projectName.trim().length > 0 ||
           formData.description.trim().length > 0 ||
           formData.customerName.trim().length > 0 ||
           formData.jobDescription.trim().length > 0;
  }, [formData]);

  // Debounced save function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveToLocalStorage();
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timeoutId);
  }, [saveToLocalStorage]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        // Only restore if data is less than 24 hours old
        if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
          setFormData(parsed.formData || formData);
          setCurrentStep(parsed.currentStep || 1);
          setUseExistingCustomer(parsed.useExistingCustomer || false);
          setSelectedCustomerId(parsed.selectedCustomerId || '');
          setCreatedProjectId(parsed.createdProjectId || null);
        }
      } catch (error) {
        console.error('Error loading saved wizard data:', error);
      }
    }
  }, []);

  // Handle page unload - ensure draft is saved if user has entered data
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasFormData()) {
        // Save current state as draft before leaving
        saveToLocalStorage();

        // Show browser confirmation dialog
        e.preventDefault();
        e.returnValue = 'Du har ulagrede endringer. Er du sikker på at du vil forlate siden?';
        return e.returnValue;
      }
    };

    const handleUnload = () => {
      if (hasFormData()) {
        // Final save attempt when page is actually unloading
        saveToLocalStorage();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [hasFormData, saveToLocalStorage]);

  // Clear localStorage on successful submission
  const clearSavedData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  // Update form data
  const updateFormData = (updates: Partial<WizardFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
      setErrors({});
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setErrors({});
    }
  };

  return (
    <PageLayout
      title="Opprett nytt prosjekt"
      showBackButton
      backUrl="/"
      headerActions={
        <div className="text-lg text-jobblogg-text-muted">
          Fyll ut informasjonen nedenfor for å starte et nytt prosjekt ✨
        </div>
      }
    >
      {/* Success Message */}
      {showSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md mx-4 text-center animate-scale-up">
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-jobblogg-text-strong mb-2">Prosjekt opprettet!</h3>
            <p className="text-jobblogg-text-muted">Ditt nye prosjekt er nå klart for bruk.</p>
          </div>
        </div>
      )}

      {/* Main Wizard Content */}
      <div className="max-w-4xl mx-auto">
        {/* Step Progress */}
        <StepProgress steps={steps} currentStep={currentStep} className="mb-8" />

        {/* Wizard Form */}
        <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border overflow-hidden">
          <div className="p-8">
            {/* Step 1: Project Details */}
            <WizardStep
              title="Prosjektdetaljer"
              description="Start med å beskrive prosjektet ditt"
              isActive={currentStep === 1}
            >
              <Step1ProjectDetails
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                onNext={goToNextStep}
              />
            </WizardStep>

            {/* Step 2: Customer Information */}
            <WizardStep
              title="Kundeinformasjon"
              description="Legg til kundedetaljer for prosjektet"
              isActive={currentStep === 2}
            >
              <Step2CustomerInfo
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                useExistingCustomer={useExistingCustomer}
                setUseExistingCustomer={setUseExistingCustomer}
                selectedCustomerId={selectedCustomerId}
                setSelectedCustomerId={setSelectedCustomerId}
                existingCustomers={existingCustomers}
                onNext={goToNextStep}
                onPrevious={goToPreviousStep}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                setErrors={setErrors}
                createProject={createProject}
                createCustomer={createCustomer}
                user={user}
                clearSavedData={clearSavedData}
                setShowSuccess={setShowSuccess}
                navigate={navigate}
                setCreatedProjectId={setCreatedProjectId}
              />
            </WizardStep>

            {/* Step 3: Job Description */}
            <WizardStep
              title="Jobbeskrivelse"
              description="Beskriv jobben som skal utføres"
              isActive={currentStep === 3}
            >
              <Step3JobDescription
                formData={formData}
                updateFormData={updateFormData}
                errors={errors}
                onPrevious={goToPreviousStep}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                setErrors={setErrors}
                updateProjectJobData={updateProjectJobData}
                createProject={createProject}
                createCustomer={createCustomer}
                user={user}
                clearSavedData={clearSavedData}
                setShowSuccess={setShowSuccess}
                navigate={navigate}
                createdProjectId={createdProjectId}
                useExistingCustomer={useExistingCustomer}
                selectedCustomerId={selectedCustomerId}
              />
            </WizardStep>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default CreateProjectWizard;


