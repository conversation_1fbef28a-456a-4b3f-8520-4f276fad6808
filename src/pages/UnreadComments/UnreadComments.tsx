import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout } from '../../components/ui/Layout/PageLayout';
import { PrimaryButton } from '../../components/ui/Button/PrimaryButton';
import { EmptyState } from '../../components/ui/EmptyState/EmptyState';


interface UnreadComment {
  _id: string;
  message: string;
  customerName: string;
  customerEmail?: string;
  createdAt: number;
  threadId?: string;
  isRootComment?: boolean;
  // Read status fields
  isReadByContractor?: boolean;
  contractorReadAt?: number;
  isReadByCustomer?: boolean;
  customerReadAt?: number;
  authorType: 'customer' | 'contractor';
  project: {
    _id: string;
    name: string;
    description?: string;
  };
}

const UnreadComments: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  
  // Queries - Get all recent customer comments for smart filtering
  const allRecentComments = useQuery(api.customerComments.getRecentCustomerComments, {
    userId: user?.id || ""
  }) as UnreadComment[] | undefined;
  
  // Mutations
  const markAsRead = useMutation(api.customerComments.markAsRead);
  const markAllAsRead = useMutation(api.customerComments.markAllAsRead);
  const addContractorReply = useMutation(api.customerComments.addContractorReply);
  const autoMarkAsReadByContractor = useMutation(api.customerComments.autoMarkAsReadByContractor);
  
  // State
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');
  const [isSubmittingReply, setIsSubmittingReply] = useState(false);
  const [markingAsRead, setMarkingAsRead] = useState<Set<string>>(new Set());
  const [activeReplies, setActiveReplies] = useState<Set<string>>(new Set()); // Track comments being replied to

  // Smart filtering: Show unread comments + comments currently being replied to
  // This prevents comments from disappearing when marked as read during reply workflow
  const displayedComments = allRecentComments?.filter(comment => {
    const isUnread = !comment.isReadByContractor;
    const isBeingRepliedTo = activeReplies.has(comment._id);
    const shouldShow = isUnread || isBeingRepliedTo;

    // Debug logging
    if (isBeingRepliedTo) {
      console.log(`Comment ${comment._id} is being replied to - keeping visible`, {
        isUnread,
        isBeingRepliedTo,
        shouldShow,
        activeRepliesSize: activeReplies.size
      });
    }

    return shouldShow;
  }) || [];

  // Count truly unread comments for header display
  const unreadCount = allRecentComments?.filter(comment => !comment.isReadByContractor).length || 0;

  // Handle mark as read
  const handleMarkAsRead = async (commentId: string) => {
    if (!user?.id) return;

    setMarkingAsRead(prev => new Set(prev).add(commentId));
    try {
      await markAsRead({ commentId, userId: user.id });
    } catch (error) {
      console.error('Error marking comment as read:', error);
    } finally {
      setMarkingAsRead(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    if (!user?.id) return;
    
    try {
      await markAllAsRead({ userId: user.id });
    } catch (error) {
      console.error('Error marking all comments as read:', error);
    }
  };

  // Handle reply submission
  const handleReplySubmit = async (commentId: string) => {
    if (!user?.id || !replyText.trim()) return;

    setIsSubmittingReply(true);
    try {
      await addContractorReply({
        parentCommentId: commentId,
        message: replyText.trim(),
        userId: user.id
      });

      // Clear reply form and remove from active replies (comment already marked as read when form was opened)
      setReplyText('');
      setReplyingTo(null);
      // Remove from active replies - comment will now be filtered out since it's read and not being replied to
      setActiveReplies(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setIsSubmittingReply(false);
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Loading state
  if (allRecentComments === undefined) {
    return (
      <PageLayout title="Nye kommentarer" showBackButton backUrl="/">
        <div className="flex justify-center py-12">
          <div className="w-8 h-8 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </PageLayout>
    );
  }

  // Empty state
  if (displayedComments.length === 0) {
    return (
      <PageLayout title="Nye kommentarer" showBackButton backUrl="/">
        <EmptyState
          title="🎉 Ingen nye kommentarer!"
          description="Du har ingen nye kommentarer fra kunder. Alle kommentarer er lest og besvart."
          actionLabel="Tilbake til dashboard"
          onAction={() => navigate('/')}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="Nye kommentarer"
      showBackButton
      backUrl="/"
      headerActions={
        <PrimaryButton
          variant="outline"
          size="sm"
          onClick={handleMarkAllAsRead}
          disabled={unreadCount === 0}
        >
          Marker alle som lest
        </PrimaryButton>
      }
    >
      <div className="space-y-6">
        {/* Summary */}
        <div className="bg-jobblogg-card rounded-xl p-6 border border-jobblogg-border">
          <h2 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
            {unreadCount} {unreadCount === 1 ? 'ulest kommentar' : 'uleste kommentarer'}
            {displayedComments.length > unreadCount && (
              <span className="text-jobblogg-text-muted text-sm font-normal ml-2">
                ({displayedComments.length - unreadCount} under behandling)
              </span>
            )}
          </h2>
          <p className="text-jobblogg-text-medium">
            Nye kommentarer fra kunder som venter på svar eller bekreftelse.
          </p>
        </div>

        {/* Comments List */}
        <div className="space-y-4">
          {displayedComments.map((comment) => (
            <div
              key={comment._id}
              className="bg-white rounded-xl p-6 border border-jobblogg-border shadow-sm hover:shadow-md transition-shadow"
            >
              {/* Comment Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-jobblogg-text-strong">
                      {comment.customerName}
                    </h3>
                    <span className="px-2 py-1 bg-jobblogg-accent-soft text-jobblogg-accent text-xs font-medium rounded-full">
                      {comment.isRootComment ? 'Ny kommentar' : 'Svar'}
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-jobblogg-text-muted">
                    <span>📅 {formatDate(comment.createdAt)}</span>
                    <button
                      onClick={() => navigate(`/project/${comment.project._id}`)}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark font-medium"
                    >
                      📁 {comment.project.name}
                    </button>
                  </div>
                </div>


              </div>

              {/* Comment Content */}
              <div className="bg-jobblogg-neutral rounded-lg p-4 mb-4">
                <p className="text-jobblogg-text-strong whitespace-pre-wrap">
                  {comment.message}
                </p>
              </div>

              {/* Reply Section */}
              <div className="border-t border-jobblogg-border pt-4">
                {replyingTo === comment._id ? (
                  <div className="space-y-3">
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder="Skriv ditt svar til kunden..."
                      className="w-full p-3 border border-jobblogg-border rounded-lg resize-none focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent"
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <PrimaryButton
                        onClick={() => handleReplySubmit(comment._id)}
                        disabled={!replyText.trim() || isSubmittingReply}
                        size="sm"
                      >
                        {isSubmittingReply ? 'Sender...' : 'Send svar'}
                      </PrimaryButton>
                      <PrimaryButton
                        variant="outline"
                        onClick={() => {
                          setReplyingTo(null);
                          setReplyText('');
                          // Remove from active replies when canceling
                          // If comment was marked as read, it will now be filtered out
                          setActiveReplies(prev => {
                            const newSet = new Set(prev);
                            newSet.delete(comment._id);
                            return newSet;
                          });
                        }}
                        size="sm"
                      >
                        Avbryt
                      </PrimaryButton>
                    </div>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <PrimaryButton
                      variant="outline"
                      onClick={() => {
                        console.log(`Starting reply to comment ${comment._id}`, {
                          isReadByContractor: comment.isReadByContractor,
                          currentActiveReplies: Array.from(activeReplies)
                        });

                        setReplyingTo(comment._id);
                        // Add to active replies to keep comment visible during reply workflow
                        setActiveReplies(prev => {
                          const newSet = new Set(prev).add(comment._id);
                          console.log(`Added comment ${comment._id} to activeReplies`, {
                            newActiveReplies: Array.from(newSet)
                          });
                          return newSet;
                        });

                        // Mark as read immediately when contractor opens reply form
                        // This provides immediate feedback to customer that comment has been seen
                        if (!comment.isReadByContractor && user?.id) {
                          console.log(`Marking comment ${comment._id} as read`);
                          handleMarkAsRead(comment._id);
                        }
                      }}
                      size="sm"
                    >
                      💬 Svar på kommentar
                    </PrimaryButton>
                    <PrimaryButton
                      variant="outline"
                      onClick={() => navigate(`/project/${comment.project._id}`)}
                      size="sm"
                    >
                      👁️ Se i prosjekt
                    </PrimaryButton>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  );
};

export default UnreadComments;
