/**
 * Customer Data Persistence Utility
 * 
 * Manages storing and retrieving customer name and email in localStorage
 * for auto-populating comment forms across the JobbLogg application.
 */

export interface CustomerData {
  name: string;
  email?: string;
}

const STORAGE_KEY = 'jobblogg_customer_data';

/**
 * Save customer data to localStorage
 */
export const saveCustomerData = (data: CustomerData): void => {
  try {
    const customerData = {
      name: data.name.trim(),
      email: data.email?.trim() || undefined,
      lastUsed: Date.now()
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(customerData));
  } catch (error) {
    console.warn('Failed to save customer data to localStorage:', error);
  }
};

/**
 * Retrieve customer data from localStorage
 */
export const getCustomerData = (): CustomerData | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return null;
    
    const data = JSON.parse(stored);
    
    // Validate the stored data structure
    if (typeof data.name !== 'string' || !data.name.trim()) {
      return null;
    }
    
    return {
      name: data.name,
      email: data.email || undefined
    };
  } catch (error) {
    console.warn('Failed to retrieve customer data from localStorage:', error);
    return null;
  }
};

/**
 * Clear customer data from localStorage
 */
export const clearCustomerData = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear customer data from localStorage:', error);
  }
};

/**
 * Check if customer data exists in localStorage
 */
export const hasCustomerData = (): boolean => {
  const data = getCustomerData();
  return data !== null && data.name.length > 0;
};

/**
 * Update only the email field while preserving the name
 */
export const updateCustomerEmail = (email: string): void => {
  const existingData = getCustomerData();
  if (existingData) {
    saveCustomerData({
      name: existingData.name,
      email: email.trim() || undefined
    });
  }
};

/**
 * Update only the name field while preserving the email
 */
export const updateCustomerName = (name: string): void => {
  const existingData = getCustomerData();
  saveCustomerData({
    name: name.trim(),
    email: existingData?.email
  });
};
