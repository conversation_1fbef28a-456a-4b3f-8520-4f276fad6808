<!DOCTYPE html>
<html>
<head>
    <title>Debug Projects</title>
</head>
<body>
    <h1>Debug: Alle prosjekter i databasen</h1>
    <div id="projects"></div>
    
    <script type="module">
        import { ConvexHttpClient } from "convex/browser";

        const client = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");

        async function loadProjects() {
            try {
                console.log("Attempting to load projects...");
                document.getElementById('projects').innerHTML = "<p>Laster prosjekter...</p>";

                const projects = await client.query("projects:debugGetAllProjects", {});
                console.log("Projects loaded:", projects);

                document.getElementById('projects').innerHTML = `
                    <h2>Totalt ${projects.length} prosjekter funnet:</h2>
                    <pre>${JSON.stringify(projects, null, 2)}</pre>
                `;
            } catch (error) {
                console.error("Error loading projects:", error);
                document.getElementById('projects').innerHTML = `
                    <h2>Feil:</h2>
                    <pre>${error.message}</pre>
                    <p>Sjekk konsollen for mer informasjon.</p>
                `;
            }
        }

        loadProjects();
    </script>
</body>
</html>
