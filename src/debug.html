<!DOCTYPE html>
<html>
<head>
    <title>Debug Projects</title>
</head>
<body>
    <h1>Debug: Alle prosjekter i databasen</h1>
    <div id="projects"></div>
    
    <script type="module">
        import { ConvexHttpClient } from "convex/browser";
        
        const client = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");
        
        async function loadProjects() {
            try {
                const projects = await client.query("projects:debugGetAllProjects", {});
                document.getElementById('projects').innerHTML = `
                    <h2>Totalt ${projects.length} prosjekter funnet:</h2>
                    <pre>${JSON.stringify(projects, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('projects').innerHTML = `
                    <h2>Feil:</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        loadProjects();
    </script>
</body>
</html>
