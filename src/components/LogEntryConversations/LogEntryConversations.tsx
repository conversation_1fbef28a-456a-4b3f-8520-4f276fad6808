import React, { useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMedium, TextMuted } from '../ui';
import { LogEntryConversation } from '../LogEntryConversation';

interface LogEntryConversationsProps {
  logEntryId: string;
  projectId: string;
  sharedId?: string;
  isPublicView?: boolean;
  userId?: string;
  isArchived?: boolean;
}

export const LogEntryConversations: React.FC<LogEntryConversationsProps> = ({
  logEntryId,
  projectId,
  sharedId,
  isPublicView = false,
  userId,
  isArchived = false
}) => {
  // Determine if this is a contractor view
  const isContractor = !isPublicView && userId;

  // Fetch comments for this log entry
  const comments = useQuery(
    isContractor 
      ? api.logEntryComments.getByLogEntry
      : api.logEntryComments.getByLogEntryShared,
    isContractor 
      ? { logEntryId: logEntryId as any, projectId: projectId as any, userId: userId! }
      : { logEntryId: logEntryId as any, projectId: projectId as any, sharedId: sharedId! }
  );

  // Mark comments as read when contractor views them
  const markAsRead = useMutation(api.logEntryComments.markAsReadByContractor);

  useEffect(() => {
    if (isContractor && comments && comments.length > 0 && userId) {
      // Mark comments as read when contractor views them
      markAsRead({
        logEntryId: logEntryId as any,
        projectId: projectId as any,
        userId
      }).catch(console.error);
    }
  }, [isContractor, comments, userId, logEntryId, projectId, markAsRead]);

  if (!comments) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-jobblogg-neutral rounded w-1/4 mb-2"></div>
          <div className="h-20 bg-jobblogg-neutral rounded"></div>
        </div>
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="text-center py-6">
        <svg className="w-8 h-8 text-jobblogg-muted mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.255-.949L5 20l1.395-3.72C7.512 15.042 9.201 14 12 14c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8c0 1.537.586 2.939 1.547 4.005" />
        </svg>
        <TextMuted>Ingen kommentarer på denne loggen ennå</TextMuted>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {comments.map((thread) => (
        <LogEntryConversation
          key={thread.threadId}
          thread={thread}
          logEntryId={logEntryId}
          projectId={projectId}
          sharedId={sharedId}
          userId={userId}
          isPublicView={isPublicView}
          isArchived={isArchived}
        />
      ))}
    </div>
  );
};

export default LogEntryConversations;
