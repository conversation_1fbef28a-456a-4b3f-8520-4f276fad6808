import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMedium, TextMuted, TextStrong, SecondaryButton, ReadStatusIndicator } from '../ui';
import { ThreadedCommentForm } from '../ThreadedCommentForm/ThreadedCommentForm';

interface Comment {
  _id: string;
  authorType: 'customer' | 'contractor';
  customerName?: string;
  customerEmail?: string;
  contractorId?: string;
  message: string;
  createdAt: number;
  parentCommentId?: string;
  // Read status fields
  isReadByContractor?: boolean;
  contractorReadAt?: number;
  isReadByCustomer?: boolean;
  customerReadAt?: number;
}

interface Thread {
  threadId: string;
  rootComment: Comment;
  replies: Comment[];
  totalReplies: number;
  lastActivity: number;
}

interface ThreadedConversationProps {
  thread: Thread;
  projectId: string;
  sharedId: string;
  isPublicView?: boolean;
  userId?: string; // For contractor view
  onDeleteComment?: (commentId: string) => void;
  isArchived?: boolean;
}

export const ThreadedConversation: React.FC<ThreadedConversationProps> = ({
  thread,
  projectId,
  sharedId,
  isPublicView = false,
  userId,
  onDeleteComment,
  isArchived = false
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  // Mutations for automatic read detection
  const autoMarkAsReadByContractor = useMutation(api.customerComments.autoMarkAsReadByContractor);
  const autoMarkAsReadByCustomer = useMutation(api.customerComments.autoMarkAsReadByCustomer);

  // Get read status for all comments in thread
  const readStatuses = useQuery(api.customerComments.getReadStatus, {
    commentIds: [thread.rootComment._id, ...thread.replies.map(r => r._id)],
    viewerType: isPublicView ? 'customer' : 'contractor',
    userId: isPublicView ? undefined : userId,
    sharedId: isPublicView ? sharedId : undefined
  });

  // Automatic read detection when component mounts or thread changes
  useEffect(() => {
    if (!readStatuses) return;

    const commentIds = [thread.rootComment._id, ...thread.replies.map(r => r._id)];
    const unreadCommentIds: string[] = [];

    // Find comments that need to be marked as read
    commentIds.forEach(commentId => {
      const status = readStatuses[commentId];
      if (!status) return;

      if (isPublicView) {
        // Customer viewing: mark contractor comments as read
        if (status.authorType === 'contractor' && !status.isReadByCustomer) {
          unreadCommentIds.push(commentId);
        }
      } else {
        // Contractor viewing: mark customer comments as read
        if (status.authorType === 'customer' && !status.isReadByContractor) {
          unreadCommentIds.push(commentId);
        }
      }
    });

    // Mark unread comments as read
    if (unreadCommentIds.length > 0) {
      if (isPublicView) {
        autoMarkAsReadByCustomer({ commentIds: unreadCommentIds, sharedId });
      } else if (userId) {
        autoMarkAsReadByContractor({ commentIds: unreadCommentIds, userId });
      }
    }
  }, [thread, readStatuses, isPublicView, userId, sharedId, autoMarkAsReadByContractor, autoMarkAsReadByCustomer]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleReplySuccess = () => {
    setShowReplyForm(false);
    setReplyingTo(null);
  };

  const handleReplyCancel = () => {
    setShowReplyForm(false);
    setReplyingTo(null);
  };

  const renderComment = (comment: Comment, isRoot: boolean = false, level: number = 0) => {
    const isCustomer = comment.authorType === 'customer';
    const maxLevel = 3; // Limit nesting depth
    const shouldNest = level < maxLevel;

    return (
      <div
        key={comment._id}
        className={`
          ${!isRoot && shouldNest ? 'ml-6 pl-4 border-l-2' : ''}
          ${isCustomer ? 'border-jobblogg-primary' : 'border-jobblogg-accent'}
          ${!isRoot ? 'mt-4' : ''}
        `}
      >
        <div className={`
          rounded-xl p-4
          ${isCustomer 
            ? 'bg-jobblogg-primary-soft border border-jobblogg-primary-soft' 
            : 'bg-jobblogg-accent-soft border border-jobblogg-accent-soft'
          }
        `}>
          {/* Author Info */}
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-3">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center
                ${isCustomer ? 'bg-jobblogg-primary text-white' : 'bg-jobblogg-accent text-white'}
              `}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <TextStrong>
                  {isCustomer ? comment.customerName : 'Kontraktør'}
                </TextStrong>
                <div className={`text-xs ${isCustomer ? 'text-jobblogg-primary' : 'text-jobblogg-accent'}`}>
                  {isCustomer ? 'Kunde' : 'Kontraktør'}
                </div>
              </div>
            </div>
            <div className="text-right">
              <TextMuted className="text-sm">
                {formatDate(comment.createdAt)}
              </TextMuted>
              {/* Read Status Indicator */}
              {readStatuses && readStatuses[comment._id] && (
                <div className="mt-1">
                  <ReadStatusIndicator
                    readStatus={readStatuses[comment._id]}
                    viewerType={isPublicView ? 'customer' : 'contractor'}
                    size="sm"
                    showBidirectional={false}
                  />
                </div>
              )}
              {!isPublicView && onDeleteComment && (
                <button
                  onClick={() => onDeleteComment(comment._id)}
                  className="text-jobblogg-error hover:text-jobblogg-error-dark text-xs mt-1 block"
                >
                  Slett
                </button>
              )}
            </div>
          </div>

          {/* Message */}
          <div className="ml-11">
            <TextMedium>{comment.message}</TextMedium>
          </div>

          {/* Reply Button - Show based on context */}
          {!isArchived && (
            (isPublicView && !isCustomer) || // Customers can reply to contractor comments
            (!isPublicView && isCustomer)    // Contractors can reply to customer comments
          ) && (
            <div className="ml-11 mt-3">
              <SecondaryButton
                size="sm"
                onClick={() => {
                  setReplyingTo(comment._id);
                  setShowReplyForm(true);
                }}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
                Svar
              </SecondaryButton>
            </div>
          )}
        </div>

        {/* Reply Form */}
        {showReplyForm && replyingTo === comment._id && (
          <ThreadedCommentForm
            projectId={projectId}
            sharedId={sharedId}
            parentCommentId={comment._id}
            isArchived={isArchived}
            threadId={thread.threadId}
            isReply={true}
            isPublicView={isPublicView}
            userId={userId}
            onSuccess={handleReplySuccess}
            onCancel={handleReplyCancel}
            placeholder={`Svar til ${isCustomer ? comment.customerName : 'kontraktør'}...`}
          />
        )}
      </div>
    );
  };

  return (
    <div className="bg-white border border-jobblogg-border rounded-xl p-6">
      {/* Root Comment */}
      {renderComment(thread.rootComment, true)}

      {/* Replies */}
      {thread.replies.map((reply, index) => {
        // Calculate nesting level based on parent relationships
        let level = 1;
        let currentReply = reply;
        
        // Find the depth of this reply
        while (currentReply.parentCommentId && currentReply.parentCommentId !== thread.rootComment._id) {
          const parentReply = thread.replies.find(r => r._id === currentReply.parentCommentId);
          if (parentReply) {
            level++;
            currentReply = parentReply;
          } else {
            break;
          }
        }

        return renderComment(reply, false, level);
      })}

      {/* Thread Stats */}
      {thread.totalReplies > 0 && (
        <div className="mt-4 pt-4 border-t border-jobblogg-border">
          <TextMuted className="text-sm">
            {thread.totalReplies} svar • Siste aktivitet: {formatDate(thread.lastActivity)}
          </TextMuted>
        </div>
      )}

      {/* Thread-level Reply Button removed to avoid confusion with individual reply buttons */}

      {/* Reply forms are now only shown inline with individual comments */}
    </div>
  );
};
