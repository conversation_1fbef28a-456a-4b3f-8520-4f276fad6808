import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextInput, TextArea, PrimaryButton, SecondaryButton, TextMedium, TextMuted, TextStrong } from '../ui';
import { saveCustomerData, getCustomerData } from '../../utils/customerDataPersistence';

interface ThreadedCommentFormProps {
  projectId: string;
  sharedId: string;
  parentCommentId?: string;
  threadId?: string;
  isReply?: boolean;
  onCancel?: () => void;
  onSuccess?: () => void;
  placeholder?: string;
  isArchived?: boolean;
}

export const ThreadedCommentForm: React.FC<ThreadedCommentFormProps> = ({
  projectId,
  sharedId,
  parentCommentId,
  threadId,
  isReply = false,
  onCancel,
  onSuccess,
  placeholder,
  isArchived = false
}) => {
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Mutations
  const addThreadedComment = useMutation(api.customerComments.addThreadedComment);
  const addCustomerReply = useMutation(api.customerComments.addCustomerReply);

  // Load saved customer data on component mount
  useEffect(() => {
    const savedData = getCustomerData();
    if (savedData) {
      setCustomerName(savedData.name);
      setCustomerEmail(savedData.email || '');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customerName.trim() || !message.trim()) {
      setSubmitError('Navn og kommentar er påkrevd');
      return;
    }

    if (message.length > 1000) {
      setSubmitError('Kommentar kan ikke være lengre enn 1000 tegn');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Save customer data for future use
      saveCustomerData({
        name: customerName.trim(),
        email: customerEmail.trim() || undefined
      });

      if (isReply && parentCommentId) {
        // Add customer reply
        await addCustomerReply({
          projectId: projectId as any,
          sharedId,
          parentCommentId: parentCommentId as any,
          customerName: customerName.trim(),
          customerEmail: customerEmail.trim() || undefined,
          message: message.trim()
        });
      } else {
        // Add new threaded comment
        await addThreadedComment({
          projectId: projectId as any,
          sharedId,
          customerName: customerName.trim(),
          customerEmail: customerEmail.trim() || undefined,
          message: message.trim()
        });
      }

      // Reset form
      setMessage('');
      setSubmitSuccess(true);
      
      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
      
      // Hide success message after 3 seconds
      setTimeout(() => setSubmitSuccess(false), 3000);
    } catch (error) {
      console.error('Failed to submit comment:', error);
      setSubmitError(error instanceof Error ? error.message : 'Kunne ikke sende kommentar');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setMessage('');
    setSubmitError(null);
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className={`bg-jobblogg-neutral rounded-xl p-6 ${isReply ? 'ml-6 mt-4' : ''}`}>
      {!isReply && (
        <div className="mb-6">
          <TextStrong className="text-lg mb-2 block">Legg til kommentar</TextStrong>
          <TextMuted>
            {isArchived
              ? 'Dette prosjektet er arkivert og kan ikke kommenteres.'
              : 'Del dine tanker eller spørsmål om prosjektet. Du kan også svare på kontraktørens svar for å starte en samtale.'
            }
          </TextMuted>
        </div>
      )}

      {isArchived ? (
        <div className="text-center py-8">
          <div className="w-12 h-12 bg-jobblogg-text-muted rounded-full flex items-center justify-center mx-auto mb-3">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <TextMedium className="text-jobblogg-text-muted">
            {isReply ? 'Kan ikke svare på arkiverte prosjekter' : 'Kan ikke kommentere arkiverte prosjekter'}
          </TextMedium>
        </div>
      ) : (
        <>
          {submitSuccess && (
            <div className="bg-jobblogg-success-soft border border-jobblogg-success rounded-lg p-3 mb-4">
              <TextMedium className="text-jobblogg-success">
                {isReply ? 'Svar sendt!' : 'Kommentar publisert!'}
              </TextMedium>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
        {/* Customer Name */}
        <div>
          <label htmlFor={`customerName-${isReply ? 'reply' : 'comment'}`} className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            Navn *
          </label>
          <TextInput
            id={`customerName-${isReply ? 'reply' : 'comment'}`}
            type="text"
            value={customerName}
            onChange={(e) => setCustomerName(e.target.value)}
            placeholder="Ditt navn"
            required
            maxLength={100}
            disabled={isSubmitting}
          />
        </div>

        {/* Customer Email (Optional) */}
        <div>
          <label htmlFor={`customerEmail-${isReply ? 'reply' : 'comment'}`} className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            E-post (valgfritt)
          </label>
          <TextInput
            id={`customerEmail-${isReply ? 'reply' : 'comment'}`}
            type="email"
            value={customerEmail}
            onChange={(e) => setCustomerEmail(e.target.value)}
            placeholder="<EMAIL>"
            maxLength={100}
            disabled={isSubmitting}
          />
          <TextMuted className="text-sm mt-1">
            E-post brukes kun for å kontakte deg ved behov
          </TextMuted>
        </div>

        {/* Message */}
        <div>
          <label htmlFor={`message-${isReply ? 'reply' : 'comment'}`} className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            {isReply ? 'Svar *' : 'Kommentar *'}
          </label>
          <TextArea
            id={`message-${isReply ? 'reply' : 'comment'}`}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={placeholder || (isReply ? 'Skriv ditt svar her...' : 'Skriv din kommentar her...')}
            rows={isReply ? 3 : 4}
            required
            maxLength={1000}
            disabled={isSubmitting}
          />
          <div className="flex justify-between items-center mt-1">
            <TextMuted className="text-sm">
              Maks 1000 tegn
            </TextMuted>
            <TextMuted className="text-sm">
              {message.length}/1000
            </TextMuted>
          </div>
        </div>

        {/* Error Message */}
        {submitError && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-lg p-3">
            <TextMedium className="text-jobblogg-error">
              {submitError}
            </TextMedium>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex items-center gap-3 pt-2">
          <PrimaryButton
            type="submit"
            disabled={isSubmitting || !customerName.trim() || !message.trim()}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sender...
              </>
            ) : (
              isReply ? 'Send svar' : 'Send kommentar'
            )}
          </PrimaryButton>
          
          {isReply && onCancel && (
            <SecondaryButton
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Avbryt
            </SecondaryButton>
          )}
        </div>
      </form>
        </>
      )}
    </div>
  );
};
