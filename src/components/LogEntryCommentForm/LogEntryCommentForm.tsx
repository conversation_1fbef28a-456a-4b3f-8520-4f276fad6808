import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMedium, TextMuted, PrimaryButton, SecondaryButton } from '../ui';

interface LogEntryCommentFormProps {
  logEntryId: string;
  projectId: string;
  sharedId: string;
  isPublicView?: boolean;
  userId?: string;
  isArchived?: boolean;
  onCommentAdded?: () => void;
  onCancel?: () => void;
  placeholder?: string;
  submitLabel?: string;
}

export const LogEntryCommentForm: React.FC<LogEntryCommentFormProps> = ({
  logEntryId,
  projectId,
  sharedId,
  isPublicView = false,
  userId,
  isArchived = false,
  onCommentAdded,
  onCancel,
  placeholder = "Skriv en kommentar om dette bildet...",
  submitLabel = "Send kommentar"
}) => {
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine if user is a contractor
  const isContractor = !isPublicView && userId;

  const addCustomerComment = useMutation(api.logEntryComments.addCustomerComment);
  const addContractorComment = useMutation(api.logEntryComments.addContractorComment);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation for contractors (only message required)
    if (isContractor) {
      if (!message.trim()) {
        setError('Kommentar er påkrevd');
        return;
      }
    } else {
      // Validation for customers (name and message required)
      if (!customerName.trim() || !message.trim()) {
        setError('Navn og kommentar er påkrevd');
        return;
      }
    }

    if (message.trim().length > 1000) {
      setError('Kommentar kan ikke være lengre enn 1000 tegn');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (isContractor) {
        // Contractor comment
        await addContractorComment({
          logEntryId: logEntryId as any,
          projectId: projectId as any,
          userId: userId!,
          message: message.trim()
        });
      } else {
        // Customer comment
        await addCustomerComment({
          logEntryId: logEntryId as any,
          projectId: projectId as any,
          sharedId,
          customerName: customerName.trim(),
          customerEmail: customerEmail.trim() || undefined,
          message: message.trim()
        });
      }

      // Reset form
      setCustomerName('');
      setCustomerEmail('');
      setMessage('');

      if (onCommentAdded) {
        onCommentAdded();
      }
    } catch (err) {
      console.error('Error adding comment:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke legge til kommentar');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isArchived) {
    return (
      <div className="bg-jobblogg-neutral rounded-xl p-6 text-center">
        <TextMuted>
          Dette prosjektet er arkivert og kan ikke kommenteres.
        </TextMuted>
      </div>
    );
  }

  return (
    <div className="bg-white border border-jobblogg-border rounded-xl p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Customer Info - Only show for customers */}
        {!isContractor && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                Ditt navn *
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-4 py-3 border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors"
                placeholder="Skriv inn ditt navn"
                required
                disabled={isSubmitting}
                maxLength={100}
              />
            </div>
            <div>
              <label htmlFor="customerEmail" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                E-post (valgfritt)
              </label>
              <input
                type="email"
                id="customerEmail"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                className="w-full px-4 py-3 border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors"
                placeholder="<EMAIL>"
                disabled={isSubmitting}
                maxLength={100}
              />
            </div>
          </div>
        )}

        {/* Comment Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            Kommentar *
          </label>
          <textarea
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full px-4 py-3 border border-jobblogg-border rounded-xl focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors resize-none"
            placeholder={placeholder}
            rows={4}
            required
            disabled={isSubmitting}
            maxLength={1000}
          />
          <div className="flex justify-between items-center mt-2">
            <TextMuted className="text-sm">
              {message.length}/1000 tegn
            </TextMuted>
            {message.length > 900 && (
              <TextMuted className="text-sm text-jobblogg-warning">
                {1000 - message.length} tegn igjen
              </TextMuted>
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-xl p-4">
            <TextMedium className="text-jobblogg-error">
              {error}
            </TextMedium>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
          {onCancel && (
            <SecondaryButton
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="sm:order-1"
            >
              Avbryt
            </SecondaryButton>
          )}
          <PrimaryButton
            type="submit"
            disabled={isSubmitting || !customerName.trim() || !message.trim()}
            className="sm:order-2"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Sender...
              </div>
            ) : (
              submitLabel
            )}
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
};

export default LogEntryCommentForm;
