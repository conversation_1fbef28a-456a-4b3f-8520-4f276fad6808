import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMedium, TextMuted, TextStrong, SecondaryButton } from '../ui';
import { LogEntryReplyForm } from '../LogEntryReplyForm/LogEntryReplyForm';

interface Comment {
  _id: string;
  authorType: 'customer' | 'contractor';
  customerName?: string;
  customerEmail?: string;
  contractorId?: string;
  message: string;
  createdAt: number;
  parentCommentId?: string;
  // Read status fields
  isReadByContractor?: boolean;
  contractorReadAt?: number;
  isReadByCustomer?: boolean;
  customerReadAt?: number;
}

interface Thread {
  threadId: string;
  rootComment: Comment;
  replies: Comment[];
}

interface LogEntryConversationProps {
  thread: Thread;
  logEntryId: string;
  projectId: string;
  sharedId?: string; // For customer view
  userId?: string; // For contractor view
  isPublicView?: boolean;
  isArchived?: boolean;
  onDeleteComment?: (commentId: string) => void;
}

export const LogEntryConversation: React.FC<LogEntryConversationProps> = ({
  thread,
  logEntryId,
  projectId,
  sharedId,
  userId,
  isPublicView = false,
  isArchived = false,
  onDeleteComment
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);

  // Mutations for automatic read detection
  const markAsReadByContractor = useMutation(api.logEntryComments.markAsReadByContractor);
  const markAsReadByCustomer = useMutation(api.logEntryComments.markAsReadByCustomer);

  // Auto-mark as read when contractor views
  useEffect(() => {
    if (!isPublicView && userId && thread.rootComment.authorType === 'customer') {
      // Mark as read when contractor opens the conversation
      markAsReadByContractor({
        logEntryId: logEntryId as any,
        projectId: projectId as any,
        userId
      }).catch(console.error);
    }
  }, [isPublicView, userId, thread.rootComment.authorType, logEntryId, projectId, markAsReadByContractor]);

  // Auto-mark as read when customer views
  useEffect(() => {
    if (isPublicView && sharedId) {
      // Mark contractor replies as read when customer views
      markAsReadByCustomer({
        logEntryId: logEntryId as any,
        projectId: projectId as any,
        sharedId
      }).catch(console.error);
    }
  }, [isPublicView, sharedId, logEntryId, projectId, markAsReadByCustomer]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleReply = (commentId: string) => {
    setReplyingTo(commentId);
    setShowReplyForm(true);
  };

  const handleReplyAdded = () => {
    setShowReplyForm(false);
    setReplyingTo(null);
  };

  const handleCancelReply = () => {
    setShowReplyForm(false);
    setReplyingTo(null);
  };

  const renderComment = (comment: Comment, isRoot: boolean = false, level: number = 0) => {
    const isCustomer = comment.authorType === 'customer';
    const maxLevel = 3; // Limit nesting depth
    const shouldNest = level < maxLevel;

    // Check if current user can reply to this comment (prevent self-replies)
    const canReply = () => {
      if (isArchived) return false;

      if (isPublicView) {
        // Customer view - can't reply to their own comments
        // We can't easily match customer names, so we'll allow all replies for now
        // The backend should handle duplicate prevention
        return true;
      } else {
        // Contractor view - can't reply to their own comments
        return comment.authorType !== 'contractor';
      }
    };

    return (
      <div
        key={comment._id}
        className={`
          ${!isRoot && shouldNest ? 'ml-6 pl-4 border-l-2' : ''}
          ${isCustomer ? 'border-jobblogg-primary' : 'border-jobblogg-accent'}
          ${!isRoot ? 'mt-4' : ''}
        `}
      >
        <div className={`
          rounded-xl p-4
          ${isCustomer 
            ? 'bg-jobblogg-primary-soft border border-jobblogg-primary-soft' 
            : 'bg-jobblogg-accent-soft border border-jobblogg-accent-soft'
          }
        `}>
          {/* Comment Header */}
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-3">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center
                ${isCustomer ? 'bg-jobblogg-primary' : 'bg-jobblogg-accent'}
              `}>
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <TextStrong className="text-sm">
                  {isCustomer ? comment.customerName : 'Entreprenør'}
                </TextStrong>
                <TextMuted className="text-xs">
                  {formatDate(comment.createdAt)}
                </TextMuted>
              </div>
            </div>

            <div className="text-right">
              {!isPublicView && onDeleteComment && (
                <button
                  onClick={() => onDeleteComment(comment._id)}
                  className="text-jobblogg-error hover:text-jobblogg-error-dark text-xs"
                >
                  Slett
                </button>
              )}
            </div>
          </div>

          {/* Message */}
          <div className="ml-11">
            <TextMedium className="text-sm">{comment.message}</TextMedium>
          </div>

          {/* Reply Button */}
          {canReply() && (
            <div className="ml-11 mt-3">
              <SecondaryButton
                onClick={() => handleReply(comment._id)}
                className="text-xs py-1 px-3"
                disabled={showReplyForm && replyingTo === comment._id}
              >
                {showReplyForm && replyingTo === comment._id ? 'Svarer...' : 'Svar'}
              </SecondaryButton>
            </div>
          )}

          {/* Reply Form */}
          {showReplyForm && replyingTo === comment._id && (
            <div className="ml-11">
              <LogEntryReplyForm
                parentCommentId={comment._id}
                logEntryId={logEntryId}
                projectId={projectId}
                sharedId={sharedId}
                userId={userId}
                isContractor={!isPublicView}
                isArchived={isArchived}
                onReplyAdded={handleReplyAdded}
                onCancel={handleCancelReply}
                placeholder={`Svar til ${isCustomer ? comment.customerName : 'entreprenør'}...`}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white border border-jobblogg-border rounded-xl p-6">
      {/* Root Comment */}
      {renderComment(thread.rootComment, true)}

      {/* Replies */}
      {thread.replies.map((reply, index) => {
        // Calculate nesting level based on parent relationships
        let level = 1;
        let currentReply = reply;
        
        // Find the depth of this reply
        while (currentReply.parentCommentId && currentReply.parentCommentId !== thread.rootComment._id) {
          const parentReply = thread.replies.find(r => r._id === currentReply.parentCommentId);
          if (parentReply) {
            level++;
            currentReply = parentReply;
          } else {
            break;
          }
        }

        return renderComment(reply, false, level);
      })}

      {/* Note: Reply functionality is handled by individual "Svar" buttons on each comment */}
    </div>
  );
};

export default LogEntryConversation;
