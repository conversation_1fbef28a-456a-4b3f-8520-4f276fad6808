import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMedium, TextMuted, PrimaryButton, SecondaryButton } from '../ui';
import { saveCustomerData, getCustomerData } from '../../utils/customerDataPersistence';

interface LogEntryReplyFormProps {
  parentCommentId: string;
  logEntryId: string;
  projectId: string;
  sharedId?: string; // For customer replies
  userId?: string; // For contractor replies
  isContractor?: boolean;
  isArchived?: boolean;
  onReplyAdded?: () => void;
  onCancel?: () => void;
  placeholder?: string;
}

export const LogEntryReplyForm: React.FC<LogEntryReplyFormProps> = ({
  parentCommentId,
  logEntryId,
  projectId,
  sharedId,
  userId,
  isContractor = false,
  isArchived = false,
  onReplyAdded,
  onCancel,
  placeholder = "Skriv et svar..."
}) => {
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addCustomerReply = useMutation(api.logEntryComments.addCustomerReply);
  const addContractorReply = useMutation(api.logEntryComments.addContractorReply);

  // Load saved customer data on component mount (only for customer replies)
  useEffect(() => {
    if (!isContractor) {
      const savedData = getCustomerData();
      if (savedData) {
        setCustomerName(savedData.name);
        setCustomerEmail(savedData.email || '');
      }
    }
  }, [isContractor]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) {
      setError('Svar kan ikke være tomt');
      return;
    }

    if (!isContractor && !customerName.trim()) {
      setError('Navn er påkrevd');
      return;
    }

    if (message.trim().length > 1000) {
      setError('Svar kan ikke være lengre enn 1000 tegn');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (isContractor && userId) {
        // Contractor reply
        await addContractorReply({
          parentCommentId: parentCommentId as any,
          logEntryId: logEntryId as any,
          projectId: projectId as any,
          userId,
          message: message.trim()
        });
      } else if (!isContractor && sharedId) {
        // Save customer data for future use
        saveCustomerData({
          name: customerName.trim(),
          email: customerEmail.trim() || undefined
        });

        // Customer reply
        await addCustomerReply({
          parentCommentId: parentCommentId as any,
          logEntryId: logEntryId as any,
          projectId: projectId as any,
          sharedId,
          customerName: customerName.trim(),
          customerEmail: customerEmail.trim() || undefined,
          message: message.trim()
        });
      } else {
        throw new Error('Manglende påkrevd informasjon for å sende svar');
      }

      // Reset only the message, keep customer info for next reply
      if (isContractor) {
        // For contractors, we don't have customer fields to preserve
        setMessage('');
      } else {
        // For customers, only reset the message
        setMessage('');
      }
      
      if (onReplyAdded) {
        onReplyAdded();
      }
    } catch (err) {
      console.error('Error adding reply:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke legge til svar');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isArchived) {
    return (
      <div className="bg-jobblogg-neutral rounded-xl p-4 text-center">
        <TextMuted className="text-sm">
          Dette prosjektet er arkivert og kan ikke kommenteres.
        </TextMuted>
      </div>
    );
  }

  return (
    <div className="bg-jobblogg-neutral rounded-xl p-4 mt-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Customer Info (only for non-contractor replies) */}
        {!isContractor && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="replyCustomerName" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                Ditt navn *
              </label>
              <input
                type="text"
                id="replyCustomerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors text-sm"
                placeholder="Skriv inn ditt navn"
                required
                disabled={isSubmitting}
                maxLength={100}
              />
            </div>
            <div>
              <label htmlFor="replyCustomerEmail" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                E-post (valgfritt)
              </label>
              <input
                type="email"
                id="replyCustomerEmail"
                value={customerEmail}
                onChange={(e) => setCustomerEmail(e.target.value)}
                className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors text-sm"
                placeholder="<EMAIL>"
                disabled={isSubmitting}
                maxLength={100}
              />
            </div>
          </div>
        )}

        {/* Reply Message */}
        <div>
          <label htmlFor="replyMessage" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
            {isContractor ? 'Ditt svar *' : 'Svar *'}
          </label>
          <textarea
            id="replyMessage"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary transition-colors resize-none text-sm"
            placeholder={placeholder}
            rows={3}
            required
            disabled={isSubmitting}
            maxLength={1000}
          />
          <div className="flex justify-between items-center mt-1">
            <TextMuted className="text-xs">
              {message.length}/1000 tegn
            </TextMuted>
            {message.length > 900 && (
              <TextMuted className="text-xs text-jobblogg-warning">
                {1000 - message.length} tegn igjen
              </TextMuted>
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error rounded-lg p-3">
            <TextMedium className="text-jobblogg-error text-sm">
              {error}
            </TextMedium>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
          {onCancel && (
            <SecondaryButton
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="text-sm py-2 px-4 sm:order-1"
            >
              Avbryt
            </SecondaryButton>
          )}
          <PrimaryButton
            type="submit"
            disabled={isSubmitting || !message.trim() || (!isContractor && !customerName.trim())}
            className="text-sm py-2 px-4 sm:order-2"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Sender...
              </div>
            ) : (
              'Send svar'
            )}
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
};

export default LogEntryReplyForm;
