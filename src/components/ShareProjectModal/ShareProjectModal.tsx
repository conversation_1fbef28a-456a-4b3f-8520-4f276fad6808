import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Modal, PrimaryButton, SecondaryButton, TextStrong, TextMedium, TextMuted, Switch, TextInput } from '../ui';

interface ShareProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: {
    _id: string;
    name: string;
    sharedId: string;
    isPubliclyShared?: boolean;
    shareSettings?: {
      allowCustomerComments: boolean;
      showContractorNotes: boolean;
      accessCount: number;
      lastAccessedAt?: number;
    };
  };
  userId: string;
}

export const ShareProjectModal: React.FC<ShareProjectModalProps> = ({
  isOpen,
  onClose,
  project,
  userId
}) => {
  const [isShared, setIsShared] = useState(project.isPubliclyShared || false);
  const [allowComments, setAllowComments] = useState(project.shareSettings?.allowCustomerComments ?? true);
  const [showNotes, setShowNotes] = useState(project.shareSettings?.showContractorNotes || false);
  const [isSaving, setIsSaving] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const updateSharingSettings = useMutation(api.projects.updateSharingSettings);

  // Generate share URL
  const shareUrl = `${window.location.origin}/shared/${project.sharedId}`;

  // Handle save settings
  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateSharingSettings({
        projectId: project._id as any,
        userId,
        isPubliclyShared: isShared,
        shareSettings: {
          allowCustomerComments: allowComments,
          showContractorNotes: showNotes,
          accessCount: project.shareSettings?.accessCount || 0,
          lastAccessedAt: project.shareSettings?.lastAccessedAt
        }
      });

      onClose();
    } catch (error) {
      console.error('Failed to update sharing settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Copy share URL to clipboard
  const copyShareUrl = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  // Reset copy success when modal opens/closes
  useEffect(() => {
    setCopySuccess(false);
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Del prosjekt">
      <div className="space-y-6">
        {/* Project Info */}
        <div className="bg-jobblogg-neutral rounded-xl p-4">
          <TextStrong className="text-lg mb-2">{project.name}</TextStrong>
          <TextMuted>
            Prosjekt-ID: {project.sharedId}
          </TextMuted>
        </div>

        {/* Enable Sharing Toggle */}
        <div className="flex items-center justify-between p-4 bg-jobblogg-neutral rounded-xl">
          <div>
            <TextStrong>Aktiver deling</TextStrong>
            <TextMuted className="text-sm">
              Tillat kunder å se prosjektet via delingslink
            </TextMuted>
          </div>
          <Switch
            checked={isShared}
            onChange={setIsShared}
            label="Aktiver deling"
          />
        </div>

        {isShared && (
          <>
            {/* Share URL */}
            <div className="space-y-3">
              <TextStrong>Delingslink</TextStrong>
              <div className="flex gap-2">
                <TextInput
                  value={shareUrl}
                  readOnly
                  className="flex-1 font-mono text-sm"
                />
                <PrimaryButton
                  onClick={copyShareUrl}
                  variant={copySuccess ? "success" : "primary"}
                  className="flex-shrink-0"
                >
                  {copySuccess ? (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Kopiert!
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Kopier
                    </>
                  )}
                </PrimaryButton>
              </div>
            </div>

            {/* Sharing Options */}
            <div className="space-y-4">
              <TextStrong>Delingsinnstillinger</TextStrong>
              
              <div className="flex items-center justify-between p-3 bg-jobblogg-neutral rounded-lg">
                <div>
                  <TextMedium>Tillat kundekommentarer</TextMedium>
                  <TextMuted className="text-sm">
                    Kunder kan legge til kommentarer på prosjektet
                  </TextMuted>
                </div>
                <Switch
                  checked={allowComments}
                  onChange={setAllowComments}
                  label="Tillat kommentarer"
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-jobblogg-neutral rounded-lg">
                <div>
                  <TextMedium>Vis kontraktørnotater</TextMedium>
                  <TextMuted className="text-sm">
                    Vis interne notater og jobbdetaljer til kunder
                  </TextMuted>
                </div>
                <Switch
                  checked={showNotes}
                  onChange={setShowNotes}
                  label="Vis notater"
                />
              </div>


            </div>

            {/* Access Statistics */}
            {project.shareSettings && (
              <div className="bg-jobblogg-neutral rounded-xl p-4">
                <TextStrong className="mb-3 block">Tilgangsstatistikk</TextStrong>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <TextMuted>Antall visninger</TextMuted>
                    <TextMedium>{project.shareSettings.accessCount || 0}</TextMedium>
                  </div>
                  <div>
                    <TextMuted>Sist åpnet</TextMuted>
                    <TextMedium>
                      {project.shareSettings.lastAccessedAt ? 
                        new Date(project.shareSettings.lastAccessedAt).toLocaleDateString('nb-NO') : 
                        'Aldri'
                      }
                    </TextMedium>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <SecondaryButton onClick={onClose} className="flex-1">
            Avbryt
          </SecondaryButton>
          <PrimaryButton 
            onClick={handleSave} 
            className="flex-1"
            disabled={isSaving}
          >
            {isSaving ? 'Lagrer...' : 'Lagre innstillinger'}
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
