import React from 'react';
import { ReadStatus } from './ReadStatus';
import { TextMuted } from '../Typography/TextMuted';

interface ReadStatusData {
  isReadByContractor: boolean;
  contractorReadAt?: number;
  isReadByCustomer: boolean;
  customerReadAt?: number;
  authorType: 'customer' | 'contractor';
}

interface ReadStatusIndicatorProps {
  /** Read status data for the comment */
  readStatus: ReadStatusData;
  /** Current viewer type */
  viewerType: 'contractor' | 'customer';
  /** Size variant */
  size?: 'sm' | 'md';
  /** Layout orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Show both read statuses or just relevant one */
  showBidirectional?: boolean;
  /** Custom className */
  className?: string;
}

export const ReadStatusIndicator: React.FC<ReadStatusIndicatorProps> = ({
  readStatus,
  viewerType,
  size = 'md',
  orientation = 'horizontal',
  showBidirectional = false,
  className = ''
}) => {
  const { 
    isReadByContractor, 
    contractorReadAt, 
    isReadByCustomer, 
    customerReadAt, 
    authorType 
  } = readStatus;

  // Determine what to show based on viewer type and comment author
  const showContractorStatus = viewerType === 'customer' && authorType === 'contractor';
  const showCustomerStatus = viewerType === 'contractor' && authorType === 'customer';

  // For bidirectional view, show both statuses
  if (showBidirectional) {
    const containerClass = orientation === 'vertical' ? 'flex flex-col gap-1' : 'flex items-center gap-3';
    
    return (
      <div className={`${containerClass} ${className}`}>
        <ReadStatus
          isRead={isReadByContractor}
          readAt={contractorReadAt}
          readerType="contractor"
          size={size}
          showTimestamp={true}
        />
        <ReadStatus
          isRead={isReadByCustomer}
          readAt={customerReadAt}
          readerType="customer"
          size={size}
          showTimestamp={true}
        />
      </div>
    );
  }

  // Show relevant status based on viewer and author
  if (showContractorStatus) {
    return (
      <ReadStatus
        isRead={isReadByContractor}
        readAt={contractorReadAt}
        readerType="contractor"
        size={size}
        showTimestamp={true}
        className={className}
      />
    );
  }

  if (showCustomerStatus) {
    return (
      <ReadStatus
        isRead={isReadByCustomer}
        readAt={customerReadAt}
        readerType="customer"
        size={size}
        showTimestamp={true}
        className={className}
      />
    );
  }

  // If no relevant status to show, return null
  return null;
};

// Utility component for showing read status in comment threads
interface ThreadReadStatusProps {
  /** Read status data for the comment */
  readStatus: ReadStatusData;
  /** Current viewer type */
  viewerType: 'contractor' | 'customer';
  /** Size variant */
  size?: 'sm' | 'md';
  /** Custom className */
  className?: string;
}

export const ThreadReadStatus: React.FC<ThreadReadStatusProps> = ({
  readStatus,
  viewerType,
  size = 'sm',
  className = ''
}) => {
  const { authorType } = readStatus;

  // For customer viewing contractor comments, show if contractor has seen customer's reply
  if (viewerType === 'customer' && authorType === 'customer') {
    return (
      <ReadStatusIndicator
        readStatus={readStatus}
        viewerType="contractor"
        size={size}
        showBidirectional={false}
        className={className}
      />
    );
  }

  // For contractor viewing customer comments, show if customer has seen contractor's reply
  if (viewerType === 'contractor' && authorType === 'contractor') {
    return (
      <ReadStatusIndicator
        readStatus={readStatus}
        viewerType="customer"
        size={size}
        showBidirectional={false}
        className={className}
      />
    );
  }

  // Default behavior
  return (
    <ReadStatusIndicator
      readStatus={readStatus}
      viewerType={viewerType}
      size={size}
      showBidirectional={false}
      className={className}
    />
  );
};

// Compact read status for dashboard/list views
interface CompactReadStatusProps {
  /** Read status data for the comment */
  readStatus: ReadStatusData;
  /** Current viewer type */
  viewerType: 'contractor' | 'customer';
  /** Custom className */
  className?: string;
}

export const CompactReadStatus: React.FC<CompactReadStatusProps> = ({
  readStatus,
  viewerType,
  className = ''
}) => {
  const { isReadByContractor, isReadByCustomer, authorType } = readStatus;

  // Determine read status based on viewer and author
  let isRead = false;
  let statusColor = 'text-jobblogg-text-muted';

  if (viewerType === 'contractor' && authorType === 'customer') {
    isRead = isReadByContractor;
  } else if (viewerType === 'customer' && authorType === 'contractor') {
    isRead = isReadByCustomer;
  }

  if (!isRead) {
    statusColor = 'text-jobblogg-accent';
  } else {
    statusColor = 'text-jobblogg-success';
  }

  return (
    <div className={`flex items-center ${className}`}>
      <div 
        className={`w-2 h-2 rounded-full ${isRead ? 'bg-jobblogg-success' : 'bg-jobblogg-accent animate-pulse'}`}
        title={isRead ? 'Lest' : 'Ulest'}
        role="img"
        aria-label={isRead ? 'Lest' : 'Ulest'}
      />
    </div>
  );
};
