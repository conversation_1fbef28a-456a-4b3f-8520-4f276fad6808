<!DOCTYPE html>
<html>
<head>
    <title>Debug Projects - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1f2937;
            margin-bottom: 24px;
        }
        pre {
            background: #f3f4f6;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
        .loading {
            color: #6b7280;
            font-style: italic;
        }
        .error {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 16px;
            border-radius: 8px;
        }
        .success {
            color: #059669;
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            padding: 16px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug: Alle prosjekter i JobbLogg databasen</h1>
        <div id="projects" class="loading">Laster prosjekter...</div>
    </div>
    
    <script type="module">
        // Import Convex client from CDN since we're outside the React app
        import { ConvexHttpClient } from "https://cdn.skypack.dev/convex/browser";
        
        const client = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");
        
        async function loadProjects() {
            const projectsDiv = document.getElementById('projects');

            try {
                console.log("🔍 Testing Dashboard query logic...");
                projectsDiv.innerHTML = '<p class="loading">🔄 Kobler til Convex database...</p>';

                // First get all projects
                const allProjects = await client.query("projects:debugGetAllProjects", {});
                console.log("✅ All projects loaded:", allProjects);

                // Then test the exact Dashboard query with the known user ID
                const userId = "user_2z2JPWDy6cWxcTfNSjM4zQBYJRg";
                const dashboardProjects = await client.query("projects:debugGetByUserWithCustomers", { userId });
                console.log("✅ Dashboard query result:", dashboardProjects);
                
                if (allProjects.length === 0) {
                    projectsDiv.innerHTML = `
                        <div class="success">
                            <h2>📭 Ingen prosjekter funnet</h2>
                            <p>Databasen er tom - ingen prosjekter er opprettet ennå.</p>
                            <p><strong>Dette forklarer hvorfor Dashboard er tom!</strong></p>
                        </div>
                    `;
                } else {
                    projectsDiv.innerHTML = `
                        <div class="success">
                            <h2>🔍 Dashboard Query Test Results</h2>
                        </div>

                        <h3>📊 Alle prosjekter i database (${allProjects.length}):</h3>
                        <pre>${JSON.stringify(allProjects, null, 2)}</pre>

                        <h3>🎯 Dashboard query resultat (${dashboardProjects.length}):</h3>
                        <pre>${JSON.stringify(dashboardProjects, null, 2)}</pre>

                        <div style="margin-top: 16px; padding: 12px; background: ${dashboardProjects.length > 0 ? '#f0fdf4' : '#fef2f2'}; border-radius: 8px;">
                            <strong>💡 Diagnose:</strong>
                            <ul style="margin: 8px 0; padding-left: 20px;">
                                <li>Totale prosjekter: ${allProjects.length}</li>
                                <li>Aktive prosjekter: ${allProjects.filter(p => !p.isArchived).length}</li>
                                <li>Dashboard query resultat: ${dashboardProjects.length}</li>
                                <li><strong>${dashboardProjects.length === 0 ? '❌ PROBLEM: Dashboard query returnerer ingen data!' : '✅ Dashboard query fungerer!'}</strong></li>
                            </ul>
                        </div>
                    `;
                }
            } catch (error) {
                console.error("❌ Error loading projects:", error);
                projectsDiv.innerHTML = `
                    <div class="error">
                        <h2>❌ Feil ved lasting av prosjekter</h2>
                        <p><strong>Feilmelding:</strong> ${error.message}</p>
                        <p><strong>Dette kan skyldes:</strong></p>
                        <ul>
                            <li>Convex deployment er ikke tilgjengelig</li>
                            <li>debugGetAllProjects funksjonen er ikke deployet</li>
                            <li>Nettverksproblemer</li>
                        </ul>
                        <p>Sjekk konsollen (F12) for mer detaljert informasjon.</p>
                    </div>
                `;
            }
        }
        
        // Start loading when page loads
        loadProjects();
    </script>
</body>
</html>
