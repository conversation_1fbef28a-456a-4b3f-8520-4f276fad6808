import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { nanoid } from "nanoid";

// Add a customer comment to a specific log entry
export const addCustomerComment = mutation({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    message: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    if (project.sharedId !== args.sharedId) {
      throw new Error("Ugyldig deling-ID");
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    if (logEntry.projectId !== args.projectId) {
      throw new Error("Loggoppføring tilhører ikke dette prosjektet");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Check if project is archived
    if (project.isArchived) {
      throw new Error("Kan ikke kommentere på arkiverte prosjekter");
    }

    // Validate input
    if (!args.message.trim()) {
      throw new Error("Kommentar kan ikke være tom");
    }

    if (args.message.trim().length > 1000) {
      throw new Error("Kommentar kan ikke være lengre enn 1000 tegn");
    }

    // Create new thread
    const threadId = nanoid(12);

    // Add the root comment
    const commentId = await ctx.db.insert("logEntryComments", {
      logEntryId: args.logEntryId,
      projectId: args.projectId,
      sharedId: args.sharedId,
      threadId,
      parentCommentId: undefined,
      isRootComment: true,
      authorType: "customer",
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      contractorId: undefined,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress,
      isReadByContractor: false, // New customer comments are unread by default
      contractorReadAt: undefined,
      isReadByCustomer: true, // Customer automatically reads their own comments
      customerReadAt: Date.now()
    });

    return { success: true, commentId, threadId };
  }
});

// Add a customer reply to an existing log entry comment thread
export const addCustomerReply = mutation({
  args: {
    parentCommentId: v.id("logEntryComments"),
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    sharedId: v.string(),
    customerName: v.string(),
    customerEmail: v.optional(v.string()),
    message: v.string(),
    ipAddress: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Verify the parent comment exists
    const parentComment = await ctx.db.get(args.parentCommentId);
    if (!parentComment) {
      throw new Error("Opprinnelig kommentar ikke funnet");
    }

    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    if (project.sharedId !== args.sharedId) {
      throw new Error("Ugyldig deling-ID");
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    if (logEntry.projectId !== args.projectId) {
      throw new Error("Loggoppføring tilhører ikke dette prosjektet");
    }

    // Verify parent comment belongs to the same log entry
    if (parentComment.logEntryId !== args.logEntryId) {
      throw new Error("Kommentar tilhører ikke denne loggoppføringen");
    }

    // Check if comments are allowed
    if (!project.shareSettings?.allowCustomerComments) {
      throw new Error("Kommentarer er ikke tillatt for dette prosjektet");
    }

    // Check if project is archived
    if (project.isArchived) {
      throw new Error("Kan ikke kommentere på arkiverte prosjekter");
    }

    // Validate input
    if (!args.message.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.message.trim().length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the reply to the same thread
    const commentId = await ctx.db.insert("logEntryComments", {
      logEntryId: args.logEntryId,
      projectId: args.projectId,
      sharedId: args.sharedId,
      threadId: parentComment.threadId,
      parentCommentId: args.parentCommentId,
      isRootComment: false,
      authorType: "customer",
      customerName: args.customerName.trim(),
      customerEmail: args.customerEmail?.trim(),
      contractorId: undefined,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: args.ipAddress,
      isReadByContractor: false, // New customer replies are unread by default
      contractorReadAt: undefined,
      isReadByCustomer: true, // Customer automatically reads their own replies
      customerReadAt: Date.now()
    });

    return { success: true, commentId };
  }
});

// Add a contractor reply to a log entry comment thread
export const addContractorReply = mutation({
  args: {
    parentCommentId: v.id("logEntryComments"),
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    userId: v.string(),
    message: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the parent comment exists
    const parentComment = await ctx.db.get(args.parentCommentId);
    if (!parentComment) {
      throw new Error("Opprinnelig kommentar ikke funnet");
    }

    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    if (logEntry.projectId !== args.projectId) {
      throw new Error("Loggoppføring tilhører ikke dette prosjektet");
    }

    // Verify parent comment belongs to the same log entry
    if (parentComment.logEntryId !== args.logEntryId) {
      throw new Error("Kommentar tilhører ikke denne loggoppføringen");
    }

    // Validate input
    if (!args.message.trim()) {
      throw new Error("Svar kan ikke være tomt");
    }

    if (args.message.trim().length > 1000) {
      throw new Error("Svar kan ikke være lengre enn 1000 tegn");
    }

    // Add the contractor reply to the same thread
    const commentId = await ctx.db.insert("logEntryComments", {
      logEntryId: args.logEntryId,
      projectId: args.projectId,
      sharedId: parentComment.sharedId,
      threadId: parentComment.threadId,
      parentCommentId: args.parentCommentId,
      isRootComment: false,
      authorType: "contractor",
      customerName: undefined,
      customerEmail: undefined,
      contractorId: args.userId,
      message: args.message.trim(),
      createdAt: Date.now(),
      ipAddress: undefined,
      isReadByContractor: true, // Contractor automatically reads their own replies
      contractorReadAt: Date.now(),
      isReadByCustomer: false, // New contractor replies are unread by customer
      customerReadAt: undefined
    });

    return { success: true, commentId };
  }
});

// Get threaded comments for a specific log entry (for contractors)
export const getByLogEntry = query({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      throw new Error("Loggoppføring ikke funnet");
    }

    if (logEntry.projectId !== args.projectId) {
      throw new Error("Loggoppføring tilhører ikke dette prosjektet");
    }

    // Get all comments for the log entry
    const allComments = await ctx.db
      .query("logEntryComments")
      .withIndex("by_log_entry", (q) => q.eq("logEntryId", args.logEntryId))
      .order("asc")
      .collect();

    // Group comments by thread
    const threadMap = new Map();
    
    for (const comment of allComments) {
      if (!threadMap.has(comment.threadId)) {
        threadMap.set(comment.threadId, {
          threadId: comment.threadId,
          rootComment: null,
          replies: []
        });
      }
      
      const thread = threadMap.get(comment.threadId);
      if (comment.isRootComment) {
        thread.rootComment = comment;
      } else {
        thread.replies.push(comment);
      }
    }

    // Convert to array and filter out threads without root comments
    const threads = Array.from(threadMap.values())
      .filter(thread => thread.rootComment !== null)
      .sort((a, b) => a.rootComment.createdAt - b.rootComment.createdAt);

    return threads;
  }
});

// Get threaded comments for a specific log entry (for public/shared view)
export const getByLogEntryShared = query({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    sharedId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return [];
    }

    if (!project.isPubliclyShared || project.sharedId !== args.sharedId) {
      return [];
    }

    // Verify the log entry exists and belongs to the project
    const logEntry = await ctx.db.get(args.logEntryId);
    if (!logEntry) {
      return [];
    }

    if (logEntry.projectId !== args.projectId) {
      return [];
    }

    // Get all comments for the log entry
    const allComments = await ctx.db
      .query("logEntryComments")
      .withIndex("by_log_entry", (q) => q.eq("logEntryId", args.logEntryId))
      .order("asc")
      .collect();

    // Group comments by thread
    const threadMap = new Map();

    for (const comment of allComments) {
      if (!threadMap.has(comment.threadId)) {
        threadMap.set(comment.threadId, {
          threadId: comment.threadId,
          rootComment: null,
          replies: []
        });
      }

      const thread = threadMap.get(comment.threadId);
      if (comment.isRootComment) {
        thread.rootComment = comment;
      } else {
        thread.replies.push(comment);
      }
    }

    // Convert to array and filter out threads without root comments
    const threads = Array.from(threadMap.values())
      .filter(thread => thread.rootComment !== null)
      .sort((a, b) => a.rootComment.createdAt - b.rootComment.createdAt);

    return threads;
  }
});

// Mark log entry comments as read by contractor
export const markAsReadByContractor = mutation({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Get all unread customer comments for this log entry
    const unreadComments = await ctx.db
      .query("logEntryComments")
      .withIndex("by_log_entry", (q) => q.eq("logEntryId", args.logEntryId))
      .filter((q) =>
        q.and(
          q.eq(q.field("authorType"), "customer"),
          q.neq(q.field("isReadByContractor"), true)
        )
      )
      .collect();

    // Mark all as read
    const now = Date.now();
    for (const comment of unreadComments) {
      await ctx.db.patch(comment._id, {
        isReadByContractor: true,
        contractorReadAt: now
      });
    }

    return { success: true, markedCount: unreadComments.length };
  }
});

// Mark log entry comments as read by customer
export const markAsReadByCustomer = mutation({
  args: {
    logEntryId: v.id("logEntries"),
    projectId: v.id("projects"),
    sharedId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project exists and is publicly shared
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (!project.isPubliclyShared || project.sharedId !== args.sharedId) {
      throw new Error("Ugyldig tilgang");
    }

    // Get all unread contractor replies for this log entry
    const unreadReplies = await ctx.db
      .query("logEntryComments")
      .withIndex("by_log_entry", (q) => q.eq("logEntryId", args.logEntryId))
      .filter((q) =>
        q.and(
          q.eq(q.field("authorType"), "contractor"),
          q.neq(q.field("isReadByCustomer"), true)
        )
      )
      .collect();

    // Mark all as read
    const now = Date.now();
    for (const reply of unreadReplies) {
      await ctx.db.patch(reply._id, {
        isReadByCustomer: true,
        customerReadAt: now
      });
    }

    return { success: true, markedCount: unreadReplies.length };
  }
});

// Get unread comment counts for all log entries in a project (for contractor)
export const getUnreadCountsByProject = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project belongs to the user
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Get all unread customer comments for this project
    const unreadComments = await ctx.db
      .query("logEntryComments")
      .withIndex("by_project_unread", (q) =>
        q.eq("projectId", args.projectId)
         .eq("isReadByContractor", false)
         .eq("authorType", "customer")
      )
      .collect();

    // Group by log entry
    const countsByEntry = new Map();
    for (const comment of unreadComments) {
      const current = countsByEntry.get(comment.logEntryId) || 0;
      countsByEntry.set(comment.logEntryId, current + 1);
    }

    // Convert to object format
    const result: Record<string, number> = {};
    for (const [logEntryId, count] of countsByEntry.entries()) {
      result[logEntryId] = count;
    }

    return result;
  }
});

// Get total unread comment count across all projects (for dashboard)
export const getUnreadCount = query({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return 0;
    }

    let totalUnreadCount = 0;

    // Get unread customer comments from all projects
    for (const project of projects) {
      const unreadComments = await ctx.db
        .query("logEntryComments")
        .withIndex("by_project_unread", (q) =>
          q.eq("projectId", project._id)
           .eq("isReadByContractor", false)
           .eq("authorType", "customer")
        )
        .collect();

      totalUnreadCount += unreadComments.length;
    }

    return totalUnreadCount;
  }
});

// Get all recent customer comments across all projects (for UnreadComments page)
export const getRecentCustomerComments = query({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    if (projects.length === 0) {
      return [];
    }

    const allComments = [];

    // Get customer comments from all projects
    for (const project of projects) {
      const comments = await ctx.db
        .query("logEntryComments")
        .withIndex("by_project", (q) => q.eq("projectId", project._id))
        .filter((q) => q.eq(q.field("authorType"), "customer"))
        .order("desc") // Most recent first
        .collect();

      // Add project context to each comment
      for (const comment of comments) {
        allComments.push({
          ...comment,
          project: {
            _id: project._id,
            name: project.name,
            description: project.description
          }
        });
      }
    }

    // Sort all comments by creation date (most recent first)
    allComments.sort((a, b) => b.createdAt - a.createdAt);

    return allComments;
  }
});

// Mark a specific comment as read by contractor
export const markAsRead = mutation({
  args: {
    commentId: v.id("logEntryComments"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Kommentar ikke funnet");
    }

    // Verify the project belongs to the user
    const project = await ctx.db.get(comment.projectId);
    if (!project || project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til denne kommentaren");
    }

    // Mark as read by contractor
    await ctx.db.patch(args.commentId, {
      isReadByContractor: true,
      contractorReadAt: Date.now()
    });

    return { success: true };
  }
});

// Mark all unread comments as read by contractor
export const markAllAsRead = mutation({
  args: {
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Get all projects for this user
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    let totalMarked = 0;

    // Mark all unread customer comments as read
    for (const project of projects) {
      const unreadComments = await ctx.db
        .query("logEntryComments")
        .withIndex("by_project_unread", (q) =>
          q.eq("projectId", project._id)
           .eq("isReadByContractor", false)
           .eq("authorType", "customer")
        )
        .collect();

      const now = Date.now();
      for (const comment of unreadComments) {
        await ctx.db.patch(comment._id, {
          isReadByContractor: true,
          contractorReadAt: now
        });
        totalMarked++;
      }
    }

    return { success: true, markedCount: totalMarked };
  }
});

// Automatically mark comments as read when viewed by contractor
export const autoMarkAsReadByContractor = mutation({
  args: {
    commentIds: v.array(v.id("logEntryComments")),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    if (args.commentIds.length === 0) {
      return { success: true, markedCount: 0 };
    }

    let markedCount = 0;
    const now = Date.now();

    for (const commentId of args.commentIds) {
      const comment = await ctx.db.get(commentId);
      if (!comment) continue;

      // Verify the user owns the project
      const project = await ctx.db.get(comment.projectId);
      if (!project || project.userId !== args.userId) continue;

      // Only mark customer comments as read by contractor
      if (comment.authorType === "customer" && !comment.isReadByContractor) {
        await ctx.db.patch(commentId, {
          isReadByContractor: true,
          contractorReadAt: now
        });
        markedCount++;
      }
    }

    return { success: true, markedCount };
  }
});
