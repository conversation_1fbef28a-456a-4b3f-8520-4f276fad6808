import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // Customer data table for AI-agent friendly structure
  customers: defineTable({
    name: v.string(),                    // Customer name (required) - shown in UI and search
    type: v.union(v.literal("privat"), v.literal("firma")), // Customer type: "privat" or "firma" - controls logic and display
    contactPerson: v.optional(v.string()), // Contact person (if type === "firma") - e.g. "<PERSON><PERSON>"
    phone: v.optional(v.string()),       // Phone number (optional) - for quick contact
    email: v.optional(v.string()),       // Email address (optional) - used in reporting or notifications
    // Enhanced address structure
    address: v.optional(v.string()),     // Legacy single address field (for backward compatibility)
    streetAddress: v.optional(v.string()), // Street address (required for new customers) - e.g. "Storgata 15"
    postalCode: v.optional(v.string()),  // Postal code (required for new customers) - e.g. "0123"
    city: v.optional(v.string()),        // City/Town (required for new customers) - e.g. "Oslo"
    entrance: v.optional(v.string()),    // Entrance/Floor info (optional) - e.g. "Oppgang A, 2. etasje"
    orgNumber: v.optional(v.string()),   // Organization number (optional, only for firma) - legal ID
    notes: v.optional(v.string()),       // Notes (optional) - free text: key code, "customer is allergic to dogs", etc.
    userId: v.string(),                  // Owner of this customer record
    createdAt: v.number()                // Creation timestamp
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_user_and_type", ["userId", "type"])
    .index("by_org_number", ["orgNumber"]),

  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")), // Reference to customer - enables AI queries like "projects for customer X"
    sharedId: v.string(),
    createdAt: v.number(),
    // Archive management - preserves data while removing from active view
    isArchived: v.optional(v.boolean()),       // Archive status - defaults to false for active projects
    archivedAt: v.optional(v.number()),        // Timestamp when project was archived
    archivedBy: v.optional(v.string()),        // User ID who archived the project
    // Project sharing configuration
    isPubliclyShared: v.optional(v.boolean()), // Enable/disable public sharing
    shareSettings: v.optional(v.object({
      allowCustomerComments: v.boolean(),      // Allow customers to add comments
      showContractorNotes: v.boolean(),        // Show contractor-specific notes to customers
      accessCount: v.number(),                 // Track how many times project was accessed
      lastAccessedAt: v.optional(v.number())   // Track last access time
    })),
    // Job information for contractor workflow documentation
    jobData: v.optional(v.object({
      jobDescription: v.string(),           // "Hva skal gjøres?" - detailed job description
      photos: v.array(v.object({           // "Bilder fra befaring" - site inspection photos
        url: v.string(),                   // Image URL from Convex storage
        note: v.optional(v.string()),      // Optional comment/note for the image
        capturedAt: v.optional(v.number()) // Timestamp when photo was taken
      })),
      accessNotes: v.string(),             // "Tilkomst og forhold" - access and site conditions
      equipmentNeeds: v.string(),          // "Hva må medbringes?" - equipment and materials needed
      unresolvedQuestions: v.string(),     // "Hva må avklares?" - questions that need clarification
      personalNotes: v.string()            // "Egne notater" - contractor's personal notes
    }))
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer", ["customerId"])
    .index("by_user_and_customer", ["userId", "customerId"])
    .index("by_user_and_archive_status", ["userId", "isArchived"])  // Efficient querying of active vs archived projects
    .index("by_archived_status", ["isArchived"]),                   // Global archive status queries

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: v.number(),
    // Edit history and tracking fields
    isEdited: v.optional(v.boolean()),
    lastEditedAt: v.optional(v.number()),
    editHistory: v.optional(v.array(v.object({
      version: v.number(),
      editedAt: v.number(),
      description: v.string(),
      imageId: v.optional(v.id("_storage")),
      changeType: v.string(), // "description", "image", "both"
      changeSummary: v.string()
    })))
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"]),

  // Threaded customer comments and contractor replies on shared projects
  customerComments: defineTable({
    projectId: v.id("projects"),
    sharedId: v.string(),                  // For validation that comment came from shared link

    // Thread management (optional for backward compatibility)
    threadId: v.optional(v.string()),                  // Unique thread identifier (nanoid) - groups related comments
    parentCommentId: v.optional(v.id("customerComments")), // Reference to parent comment for replies
    isRootComment: v.optional(v.boolean()),            // True for initial comments, false for replies

    // Author information (optional for backward compatibility)
    authorType: v.optional(v.union(v.literal("customer"), v.literal("contractor"))), // Who wrote this comment
    customerName: v.optional(v.string()),   // Name provided by customer (for customer comments)
    customerEmail: v.optional(v.string()),  // Optional email for notifications (for customer comments)
    contractorId: v.optional(v.string()),   // User ID of contractor (for contractor comments)

    // Content and metadata (optional for backward compatibility)
    message: v.optional(v.string()),                   // The actual comment/reply message
    createdAt: v.number(),
    ipAddress: v.optional(v.string()),     // For spam prevention (customer comments only)

    // Bidirectional read tracking
    isReadByContractor: v.optional(v.boolean()),       // Whether contractor has read this comment (defaults to false for customer comments)
    contractorReadAt: v.optional(v.number()),          // Timestamp when contractor marked as read
    isReadByCustomer: v.optional(v.boolean()),         // Whether customer has read this comment (defaults to false for contractor replies)
    customerReadAt: v.optional(v.number()),            // Timestamp when customer marked as read

    // Legacy read tracking (for backward compatibility)
    readAt: v.optional(v.number()),                    // Legacy timestamp field - will be migrated to contractorReadAt

    // Legacy fields for backward compatibility (will be migrated)
    isApproved: v.optional(v.boolean()),
    comment: v.optional(v.string()),       // Legacy field - will migrate to 'message'
    contractorReply: v.optional(v.object({
      message: v.string(),
      repliedAt: v.number(),
      repliedBy: v.string()
    }))
  })
    .index("by_project", ["projectId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_thread", ["threadId"])
    .index("by_parent", ["parentCommentId"])
    .index("by_project_and_thread", ["projectId", "threadId"])
    .index("by_project_root_comments", ["projectId", "isRootComment"])
    // Contractor read status indexes
    .index("by_unread_contractor", ["isReadByContractor", "authorType"])
    .index("by_project_unread", ["projectId", "isReadByContractor", "authorType"])
    // Customer read status indexes
    .index("by_unread_customer", ["isReadByCustomer", "authorType"])
    .index("by_project_customer_unread", ["projectId", "isReadByCustomer", "authorType"])
    // Shared project customer read tracking
    .index("by_shared_customer_unread", ["sharedId", "isReadByCustomer", "authorType"])
});
