import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { nanoid } from 'nanoid';

// Generate upload URL for file storage
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")) // Optional customer reference
  },
  handler: async (ctx, args) => {
    // If customerId is provided, verify it exists and belongs to the user
    if (args.customerId) {
      const customer = await ctx.db.get(args.customerId);
      if (!customer) {
        throw new Error("Kunde ikke funnet");
      }
      if (customer.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til denne kunden");
      }
    }

    return await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      userId: args.userId,
      customerId: args.customerId,
      sharedId: nanoid(10),
      createdAt: Date.now()
    });
  }
});

export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
      .order("desc")
      .collect();
  }
});

// Get projects with customer data (AI-agent friendly) - excludes archived projects
export const getByUserWithCustomers = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
      .order("desc")
      .collect();

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      projects.map(async (project) => {
        if (project.customerId) {
          const customer = await ctx.db.get(project.customerId);
          return {
            ...project,
            customer
          };
        }
        return {
          ...project,
          customer: null
        };
      })
    );

    return projectsWithCustomers;
  }
});

// Get archived projects for a user
export const getArchivedByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", true))
      .order("desc")
      .collect();
  }
});

// Get archived projects with customer data
export const getArchivedByUserWithCustomers = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", true))
      .order("desc")
      .collect();

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      projects.map(async (project) => {
        if (project.customerId) {
          const customer = await ctx.db.get(project.customerId);
          return {
            ...project,
            customer
          };
        }
        return {
          ...project,
          customer: null
        };
      })
    );

    return projectsWithCustomers;
  }
});

// Get all projects (active and archived) for a user - for search/admin purposes
export const getAllByUser = query({
  args: {
    userId: v.string(),
    includeArchived: v.optional(v.boolean()) // Optional flag to include archived projects
  },
  handler: async (ctx, args) => {
    if (args.includeArchived) {
      // Return all projects (active and archived)
      return await ctx.db
        .query("projects")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .order("desc")
        .collect();
    } else {
      // Return only active projects (default behavior)
      return await ctx.db
        .query("projects")
        .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
        .order("desc")
        .collect();
    }
  }
});

export const getById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Fetch customer data if project has a customer
    if (project.customerId) {
      const customer = await ctx.db.get(project.customerId);
      return {
        ...project,
        customer
      };
    }

    return {
      ...project,
      customer: null
    };
  }
});

// Get projects by customer (AI-agent friendly for queries like "projects for customer X") - excludes archived
export const getByCustomer = query({
  args: {
    customerId: v.id("customers"),
    userId: v.string(),
    includeArchived: v.optional(v.boolean()) // Optional flag to include archived projects
  },
  handler: async (ctx, args) => {
    // Verify customer belongs to user
    const customer = await ctx.db.get(args.customerId);
    if (!customer || customer.userId !== args.userId) {
      throw new Error("Kunde ikke funnet eller du har ikke tilgang");
    }

    let projects;
    if (args.includeArchived) {
      // Get all projects for this customer (active and archived)
      projects = await ctx.db
        .query("projects")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
        .order("desc")
        .collect();
    } else {
      // Get only active projects for this customer (default)
      projects = await ctx.db
        .query("projects")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
        .filter((q) => q.neq(q.field("isArchived"), true))
        .order("desc")
        .collect();
    }

    return projects.map(project => ({
      ...project,
      customer
    }));
  }
});

// Update project job data
export const updateProjectJobData = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    jobData: v.object({
      jobDescription: v.string(),
      photos: v.array(v.object({
        url: v.string(),
        note: v.optional(v.string()),
        capturedAt: v.optional(v.number())
      })),
      accessNotes: v.string(),
      equipmentNeeds: v.string(),
      unresolvedQuestions: v.string(),
      personalNotes: v.string()
    })
  },
  handler: async (ctx, args) => {
    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å oppdatere dette prosjektet");
    }

    // Update the project with job data
    await ctx.db.patch(args.projectId, {
      jobData: args.jobData
    });

    return { success: true };
  }
});

// Store uploaded image file and return URL
export const storeJobImage = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    storageId: v.id("_storage")
  },
  handler: async (ctx, args) => {
    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å laste opp bilder til dette prosjektet");
    }

    // Get the URL for the stored file
    const url = await ctx.storage.getUrl(args.storageId);

    if (!url) {
      throw new Error("Kunne ikke hente bilde-URL");
    }

    return { url };
  }
});

// Archive a project (preserves all data but removes from active view)
export const archiveProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // First, verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å arkivere dette prosjektet");
    }

    // Check if project is already archived
    if (project.isArchived) {
      throw new Error("Prosjektet er allerede arkivert");
    }

    // Archive the project (preserves all data)
    await ctx.db.patch(args.projectId, {
      isArchived: true,
      archivedAt: Date.now(),
      archivedBy: args.userId
    });

    return { success: true };
  }
});

// Restore an archived project (makes it active again)
export const restoreProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å gjenåpne dette prosjektet");
    }

    // Check if project is actually archived
    if (!project.isArchived) {
      throw new Error("Prosjektet er ikke arkivert");
    }

    // Restore the project (make it active again)
    await ctx.db.patch(args.projectId, {
      isArchived: false,
      archivedAt: undefined,
      archivedBy: undefined
    });

    return { success: true };
  }
});

// Get project by shared ID (for public access)
export const getBySharedId = query({
  args: { sharedId: v.string() },
  handler: async (ctx, args) => {
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();

    if (!project) {
      return null;
    }

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      return null;
    }

    // Check if sharing has expired
    if (project.shareSettings?.expiresAt && project.shareSettings.expiresAt < Date.now()) {
      return null;
    }

    // Note: Access count increment would be done in a mutation, not a query
    // For now, we'll just return the project data without incrementing

    // Fetch customer data if project has a customer
    if (project.customerId) {
      const customer = await ctx.db.get(project.customerId);
      return {
        ...project,
        customer
      };
    }

    return {
      ...project,
      customer: null
    };
  }
});

// Enable/disable project sharing
export const updateSharingSettings = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    isPubliclyShared: v.boolean(),
    shareSettings: v.optional(v.object({
      allowCustomerComments: v.boolean(),
      showContractorNotes: v.boolean(),
      expiresAt: v.optional(v.number()),
      accessCount: v.number(),
      lastAccessedAt: v.optional(v.number())
    }))
  },
  handler: async (ctx, args) => {
    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å endre delingsinnstillinger for dette prosjektet");
    }

    // Update sharing settings
    await ctx.db.patch(args.projectId, {
      isPubliclyShared: args.isPubliclyShared,
      shareSettings: args.shareSettings || {
        allowCustomerComments: false,
        showContractorNotes: false,
        accessCount: 0
      }
    });

    return { success: true };
  }
});
